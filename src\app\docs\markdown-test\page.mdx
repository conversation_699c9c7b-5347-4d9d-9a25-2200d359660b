# Simple Markdown Test

This is a simple test to verify our markdown renderer is working correctly.

## Basic Elements

**Bold text** and *italic text* work perfectly.

Here's some `inline code` and a [link to the main docs](/docs).

### Lists

- Item 1
- Item 2
- Item 3

1. First
2. Second
3. Third

### Code Block

```javascript
function hello() {
  console.log("Hello, World!");
}
```

### Table

| Name | Age | City |
|------|-----|------|
| <PERSON> | 25  | NYC  |
| Jane | 30  | LA   |

### Blockquote

> This is a blockquote with some important information.

---

That's it! Simple and clean.
